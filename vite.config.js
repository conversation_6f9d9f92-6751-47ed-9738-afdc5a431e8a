import { defineConfig } from 'vite';
import { readFileSync, existsSync, mkdirSync, readdirSync, copyFileSync } from 'fs';
import { resolve, join } from 'path';

export default defineConfig({
    plugins: [
        {
            name: 'copy-tabler-assets',
            generateBundle() {
                // Copy Tabler CSS
                this.emitFile({
                    type: 'asset',
                    fileName: 'tabler.min.css',
                    source: readFileSync(resolve('node_modules/@tabler/core/dist/css/tabler.min.css'))
                });

                // Copy Tabler JS
                this.emitFile({
                    type: 'asset',
                    fileName: 'tabler.min.js',
                    source: readFileSync(resolve('node_modules/@tabler/core/dist/js/tabler.min.js'))
                });

                // Copy additional Tabler CSS files (optional)
                const additionalCssFiles = [
                    'tabler-flags.min.css',
                    'tabler-payments.min.css',
                    'tabler-socials.min.css',
                    'tabler-themes.min.css'
                ];

                additionalCssFiles.forEach(fileName => {
                    try {
                        this.emitFile({
                            type: 'asset',
                            fileName: fileName,
                            source: readFileSync(resolve(`node_modules/@tabler/core/dist/css/${fileName}`))
                        });
                    } catch (e) {
                        console.log(`Optional file ${fileName} not found, skipping...`);
                    }
                });
            }
        },
        {
            name: 'copy-tabler-images',
            writeBundle() {
                // Copy image assets using fs operations since they're directories
                const copyDir = (src, dest) => {
                    if (!existsSync(src)) return;

                    if (!existsSync(dest)) {
                        mkdirSync(dest, { recursive: true });
                    }

                    const entries = readdirSync(src, { withFileTypes: true });

                    for (let entry of entries) {
                        const srcPath = join(src, entry.name);
                        const destPath = join(dest, entry.name);

                        if (entry.isDirectory()) {
                            copyDir(srcPath, destPath);
                        } else {
                            copyFileSync(srcPath, destPath);
                        }
                    }
                };

                // Copy image directories
                const imgSrc = resolve('node_modules/@tabler/core/dist/img');
                const imgDest = resolve('public/vendor/tabler/img');
                copyDir(imgSrc, imgDest);
            }
        }
    ],
    build: {
        outDir: 'public/vendor/tabler',
        rollupOptions: {
            output: {
                assetFileNames: '[name].[ext]'
            }
        }
    }
});
