import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/css/app.css', 'resources/js/app.js'],
            refresh: true,
        }),
        {
            name: 'copy-tabler-assets',
            generateBundle() {
                this.emitFile({
                    type: 'asset',
                    fileName: 'tabler.min.css',
                    source: require('fs').readFileSync('node_modules/@tabler/core/dist/css/tabler.min.css')
                });
                this.emitFile({
                    type: 'asset',
                    fileName: 'tabler.min.js',
                    source: require('fs').readFileSync('node_modules/@tabler/core/dist/js/tabler.min.js')
                });
            }
        }
    ],
    build: {
        outDir: 'public/assets'
    }
});
