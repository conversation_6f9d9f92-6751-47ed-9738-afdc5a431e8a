import { defineConfig } from 'vite';
import { readFileSync, existsSync, mkdirSync, readdirSync, copyFileSync } from 'fs';
import { resolve, join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __dirname = dirname(fileURLToPath(import.meta.url));

export default defineConfig({
    publicDir: false, // Disable public directory to avoid conflicts
    build: {
        outDir: 'public/vendor/tabler',
        emptyOutDir: true,
        lib: {
            entry: resolve(__dirname, 'dummy-entry.js'),
            name: 'TablerAssets',
            fileName: 'dummy'
        },
        rollupOptions: {
            output: {
                assetFileNames: '[name].[ext]'
            }
        }
    },
    plugins: [
        {
            name: 'copy-tabler-assets',
            generateBundle() {
                const tablerPath = 'node_modules/@tabler/core/dist';

                // Copy main Tabler files
                const mainFiles = [
                    { src: 'css/tabler.min.css', dest: 'tabler.min.css' },
                    { src: 'js/tabler.min.js', dest: 'tabler.min.js' }
                ];

                mainFiles.forEach(({ src, dest }) => {
                    try {
                        this.emitFile({
                            type: 'asset',
                            fileName: dest,
                            source: readFileSync(resolve(`${tablerPath}/${src}`))
                        });
                    } catch (e) {
                        console.warn(`Failed to copy ${src}:`, e.message);
                    }
                });

                // Copy additional Tabler CSS files
                const additionalCssFiles = [
                    'tabler-flags.min.css',
                    'tabler-payments.min.css',
                    'tabler-socials.min.css',
                    'tabler-themes.min.css'
                ];

                additionalCssFiles.forEach(fileName => {
                    try {
                        this.emitFile({
                            type: 'asset',
                            fileName: fileName,
                            source: readFileSync(resolve(`${tablerPath}/css/${fileName}`))
                        });
                    } catch (e) {
                        console.log(`Optional file ${fileName} not found, skipping...`);
                    }
                });
            }
        },
        {
            name: 'copy-tabler-images',
            writeBundle() {
                console.log('Copying Tabler image assets...');

                const copyDir = (src, dest) => {
                    if (!existsSync(src)) {
                        console.warn(`Source directory not found: ${src}`);
                        return;
                    }

                    if (!existsSync(dest)) {
                        mkdirSync(dest, { recursive: true });
                    }

                    const entries = readdirSync(src, { withFileTypes: true });
                    let copiedCount = 0;

                    for (let entry of entries) {
                        const srcPath = join(src, entry.name);
                        const destPath = join(dest, entry.name);

                        if (entry.isDirectory()) {
                            copyDir(srcPath, destPath);
                        } else {
                            copyFileSync(srcPath, destPath);
                            copiedCount++;
                        }
                    }

                    if (copiedCount > 0) {
                        console.log(`Copied ${copiedCount} files from ${src}`);
                    }
                };

                // Copy image directories
                const imgSrc = resolve('node_modules/@tabler/core/dist/img');
                const imgDest = resolve('public/vendor/tabler/img');
                copyDir(imgSrc, imgDest);

                console.log('Tabler image assets copied successfully!');
            }
        }
    ]
});
