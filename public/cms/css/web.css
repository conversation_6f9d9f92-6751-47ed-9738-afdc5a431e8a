a:hover {
    text-decoration: none;
}

.user-dashboard [data-bs-theme=dark] .navbar-brand-autodark .navbar-brand-image {
    filter: unset !important;
}

#modal-deposit .modal-header {
    padding: 0 .375rem 0 1.5rem;
}

#modal-deposit .card-custom .list-info {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    gap: 20px;
}

#modal-deposit .btn-copy {
    margin-left: -25px;
    padding: 2px 8px;
    font-size: 12px;
    background: #c9f7f5;
    color: #008a84;
    border: none;
}

#modal-deposit .btn-copy:hover {
    background: #adfcf8;
}

table code {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 6px;
}


.datatable-header {
    padding: 10px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #dddfe5;
}

.datatable-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px 10px;
}

.datatable-footer .dataTables_length label {
    display: flex;
    align-items: center;
    text-wrap: nowrap;
    gap: 8px;
}

.dataTables_paginate .pagination {
    gap: 5px;
}

.datatable_actions {
    list-style: none;
    padding-left: 0;
    gap: 8px;
    justify-content: center;
    font-size: 16px;
}

.btn-sm {
    padding: 4px 10px;
    border-radius: 4px;
}

.campaign-page .row-cards .card-title {
    font-size: 14px;
    font-weight: 500;
}

.campaign-page .row-cards .h1 {
    font-size: 28px;
    font-weight: 900;
}

.campaign-page .row-cards .card-footer {
    border: none;
    padding: 0;
}

.campaign-page .row-cards .btn-action-card {
    border-radius: 0;
}

#offcanvasListPosts {
    width: 60%;
}

body {
    --ck-z-default: 100;
    --ck-z-modal: calc(var(--ck-z-default) + 999);
}

.form-check-input {
    cursor: pointer;
}

.dataTable tr {
    vertical-align: middle;
}

#offcanvasCartOrder {
    position: fixed;
    bottom: -200px;
    left: 0;
    width: 100%;
    background: #fff;
    padding: 20px;
    font-size: 16px;
    box-shadow: 0 0 30px 0 rgb(162 162 162 / 23%);
    transition: all .3s;
}

#offcanvasCartOrder.active {
    bottom: 0;
    transition: all .3s;
    z-index: 100;
}

#user-site-table thead th {
    cursor: pointer;
}

#user-site-table thead th.sorting {
    color: #3a4859 !important;
}

#user-site-table thead th.sorting.sorting_desc,
#user-site-table thead th.sorting.sorting_asc {
    position: relative;
    color: #d63939 !important;
}

#user-site-table thead th.sorting.sorting_desc::after,
#user-site-table thead th.sorting.sorting_asc::after {
    position: absolute;
    content: "↑";
    margin-left: 5px;
}


#user-site-table thead th.sorting.sorting_desc::after {
    transform: rotate(180deg);
}
