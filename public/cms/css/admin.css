body.auth-page {
    background-color: var(--tblr-body-color);
}

a {
    text-decoration: none;
}

sup.required {
    color: red;
}

.badge.bg-secondary {
    color: #ffffff;
}

.page-header .page-title {
    text-transform: capitalize;
    margin-top: 6px;
    font-size: 1.15rem;
}

.datatable-header {
    padding: 10px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #dddfe5;
}

.datatable-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px 10px;
}

.datatable-footer .dataTables_length label {
    display: flex;
    align-items: center;
    text-wrap: nowrap;
    gap: 8px;
}

.dataTables_paginate .pagination {
    gap: 5px;
}

.datatable_actions {
    list-style: none;
    padding-left: 0;
    gap: 8px;
    justify-content: center;
    font-size: 16px;
}

.btn-sm {
    padding: 4px 10px;
    border-radius: 4px;
}
