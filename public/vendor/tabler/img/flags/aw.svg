<svg xmlns="http://www.w3.org/2000/svg" width="20" height="15" fill="none"><mask id="a" width="20" height="15" x="0" y="0" maskUnits="userSpaceOnUse"><path fill="#fff" d="M0 0h20v15H0z"/></mask><g mask="url(#a)"><path fill="#5BA3DA" fill-rule="evenodd" d="M0 0v15h20V0H0z" clip-rule="evenodd"/><mask id="b" width="20" height="15" x="0" y="0" maskUnits="userSpaceOnUse"><path fill="#fff" fill-rule="evenodd" d="M0 0v15h20V0H0z" clip-rule="evenodd"/></mask><g fill-rule="evenodd" clip-rule="evenodd" mask="url(#b)"><g filter="url(#c)"><path fill="#EF2929" d="M3.546 4.975.808 4.397l2.772-.549.8-2.868.633 2.845 2.474.575-2.445.575-.706 2.34-.791-2.34z"/><path fill="red" d="M3.546 4.975.808 4.397l2.772-.549.8-2.868.633 2.845 2.474.575-2.445.575-.706 2.34-.791-2.34z"/></g><path fill="#FAD615" d="M20 9H0v1h20V9zm0 2H0v1h20v-1z"/></g></g><defs><filter id="c" width="8.681" height="8.335" x="-.192" y="-.02" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset/><feGaussianBlur stdDeviation=".5"/><feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.2 0"/><feBlend in2="BackgroundImageFix" result="effect1_dropShadow"/><feBlend in="SourceGraphic" in2="effect1_dropShadow" result="shape"/></filter></defs></svg>