# Tabler Core Build Setup

This project is configured to automatically build and copy Tabler Core assets to the `public/assets` directory during the build process.

## What Gets Built

The build process copies the following Tabler assets:

### CSS Files
- `tabler.min.css` - Main Tabler CSS framework
- `tabler-flags.min.css` - Country flag icons
- `tabler-payments.min.css` - Payment method icons  
- `tabler-socials.min.css` - Social media icons
- `tabler-themes.min.css` - Theme variations

### JavaScript Files
- `tabler.min.js` - Main Tabler JavaScript functionality

### Image Assets
- `img/flags/` - Country flag SVG files
- `img/payments/` - Payment method icons
- `img/social/` - Social media icons

## Build Commands

```bash
# Build all assets (including Tabler)
npm run build

# Alternative command (same as build)
npm run build:tabler

# Clean assets directory
npm run clean:assets

# Development mode
npm run dev
```

## Output Location

All Tabler assets are built to: `public/assets/`

## Usage in Laravel Views

After building, you can reference the assets in your Laravel Blade templates:

```html
<!-- CSS -->
<link href="{{ asset('assets/tabler.min.css') }}" rel="stylesheet">
<link href="{{ asset('assets/tabler-flags.min.css') }}" rel="stylesheet">

<!-- JavaScript -->
<script src="{{ asset('assets/tabler.min.js') }}"></script>

<!-- Images -->
<img src="{{ asset('assets/img/flags/us.svg') }}" alt="US Flag">
```

## Configuration

The build configuration is in `vite.config.js`. You can modify it to:
- Add/remove specific CSS files
- Change output directory
- Add additional asset types

## Troubleshooting

If the build fails:
1. Make sure `@tabler/core` is installed: `npm install`
2. Check that `node_modules/@tabler/core/dist/` exists
3. Verify file permissions on the `public/assets/` directory
4. Run `npm run clean:assets` and try building again
