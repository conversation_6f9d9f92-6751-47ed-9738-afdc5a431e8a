@extends('core/base::layouts.auth')
@section('page_title', 'Đặt lại mật khẩu')

@section('content')
<div class="page page-center">
    <div class="container container-tight py-4">
        <div class="text-center mb-4">
            <a href="{{ route('public.index') }}" class="navbar-brand navbar-brand-autodark">
                <img src="{{ asset('cms/img/logo.png') }}" height="68" alt="{{ config('core.base.common.app_name') }}" />
            </a>
        </div>
        <form class="card card-md" action="{{ route('forgot.password.update') }}" method="POST">
            @csrf
            <div class="card-body">
                <h2 class="card-title text-center mb-4">Đặt lại mật khẩu</h2>
                <input type="hidden" name="token" value="{{ $token }}">
                <input type="hidden" name="email" value="{{ $email }}">
                <div class="mb-3">
                    <label class="form-label" for="password">Nhậ<PERSON> mật khẩu</label>
                    <div class="input-group input-group-flat">
                        <input type="password" name="password" id="password" class="form-control" placeholder="Mật khẩu mới" value="{{ old('password') }}">
                        <span class="input-group-text cursor-pointer" onclick="togglePassword('password', this)">
                            <i class="ti ti-eye-off"></i>
                        </span>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label" for="password_confirmation">Xác nhận mật khẩu</label>
                    <div class="input-group input-group-flat">
                        <input type="password" name="password_confirmation" id="password_confirmation" class="form-control" placeholder="Nhập mật khẩu mới" value="{{ old('password_confirmation') }}">
                        <span class="input-group-text cursor-pointer" onclick="togglePassword('password_confirmation', this)">
                            <i class="ti ti-eye-off"></i>
                        </span>
                    </div>
                </div>
                <div class="form-footer">
                    <button type="submit" class="btn btn-primary w-100">
                        Đặt lại mật khẩu
                    </button>
                </div>
            </div>
        </form>
        <div class="text-center text-secondary mt-3">
            <a class="text-white" href="/">Quay lại trang chủ</a>
        </div>
    </div>
</div>

@include('core/base::layouts.partials.scripts')
<script type="text/javascript">
    function togglePassword(fieldId, iconElement) {
        const passwordField = document.getElementById(fieldId);
        const icon = iconElement.querySelector('i');

        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            icon.classList.remove('ti-eye-off');
            icon.classList.add('ti-eye');
        } else {
            passwordField.type = 'password';
            icon.classList.remove('ti-eye');
            icon.classList.add('ti-eye-off');
        }
    }
</script>
@endsection