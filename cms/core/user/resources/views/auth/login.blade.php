@extends('core/base::layouts.auth')
@section('page_title', 'Đăng nhập')

@section('content')
<div class="page page-center">
    <div class="container container-tight py-4">
        <div class="text-center mb-4">
            <a href="{{ homepage_url() }}" class="navbar-brand navbar-brand-autodark">
                <img src="{{ asset('cms/img/logo.png') }}" height="68" alt="{{ config('core.base.common.app_name') }}" />
            </a>
        </div>
        <div class="card card-md">
            <div class="card-body">
                <h2 class="h2 text-center mb-4">Đ<PERSON><PERSON> nhập vào hệ thống</h2>
                <form action="{{ route('login.post') }}" method="post" autocomplete="off" novalidate>
                    @csrf
                    <div class="mb-3">
                        <label class="form-label">Tên đăng nhập / Email</label>
                        <input type="text" name="login" value="{{ old('login') }}" class="form-control" placeholder="Nhập Tên đăng nhập hoặc Địa chỉ email" autocomplete="off" />
                        @if ($errors->first('login'))
                        <div class="form-text text-danger">{{ $errors->first('login') }}</div>
                        @endif
                    </div>
                    <div class="mb-2">
                        <label class="form-label">
                            Mật khẩu
                            <span class="form-label-description">
                                <a href="{{ route('forgot') }}">Quên mật khẩu?</a>
                            </span>
                        </label>
                        <div class="input-group input-group-flat">
                            <input type="password" name="password" class="form-control" placeholder="Nhập mật khẩu" autocomplete="off" />
                        </div>
                        @if ($errors->first('password'))
                        <div class="form-text text-danger">{{ $errors->first('password') }}</div>
                        @endif
                    </div>
                    <div class="mb-2">
                        <label class="form-check">
                            <input type="checkbox" name="remember" class="form-check-input" checked />
                            <span class="form-check-label">Ghi nhớ</span>
                        </label>
                    </div>
                    <div class="form-footer">
                        <button type="submit" class="btn btn-primary w-100">
                            Đăng nhập
                        </button>
                    </div>
                </form>
            </div>
            {{-- <div class="hr-text">hoặc</div>
            <div class="card-body">
                <div class="row">
                    <div class="col">
                        <a href="#" class="btn w-100 bg-red text-white disabled" title="Đang phát triển" data-bs-toggle="tooltip" data-bs-placement="bottom">
                            <em class="ti ti-brand-google-filled me-2"></em>
                            Đăng nhập bằng Google
                        </a>
                    </div>
                </div>
            </div> --}}
        </div>
        <div class="text-center mt-3 text-white">
            Bạn chưa có tài khoản?
            <a href="{{ route('register') }}" tabindex="-1" class="text-white"><strong>Đăng ký ngay</strong></a>
        </div>
    </div>
</div>

<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1000">
    <div id="toast-admin" class="toast align-items-center text-white bg-info border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body"></div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>
<script src="{{ asset('assets/js/tabler.min.js') }}"></script>
<script src="{{ asset('cms/js/admin.js') }}"></script>
<script type="text/javascript">
    $(document).ready(() => {
        @if (session('flash_data'))
            @php
                $flash_data = session('flash_data');
            @endphp
            showNotify("{{ $flash_data['message'] }}", "{{ $flash_data['type'] }}");
        @endif
    });
</script>
@endsection
