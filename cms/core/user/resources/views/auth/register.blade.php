@extends('core/base::layouts.auth')
@section('page_title', 'Đăng ký tài khoản')

@section('content')
<div class="page page-center">
    <div class="container container-tight py-4">
        <div class="text-center mb-4">
            <a href="{{ route('public.index') }}" class="navbar-brand navbar-brand-autodark">
                <img src="{{ asset('cms/img/logo.png') }}" height="68" alt="{{ config('core.base.common.app_name') }}" />
            </a>
        </div>
        <div class="card card-md">
            <div class="card-body">
                <h2 class="h2 text-center mb-4">Đăng ký tài khoản</h2>
                <form action="{{ route('register.submit') }}" method="post" autocomplete="off" novalidate>
                    @csrf
                    <div class="mb-3">
                        <label class="form-label required">Tê<PERSON> đăng nhập</label>
                        <input type="text" name="username" value="{{ old('username') }}" class="form-control" placeholder="Nhập Tên đăng nhập" autocomplete="new-password" value="{{ old('username') }}" />
                        @if ($errors->first('username'))
                        <div class="form-text text-danger">{{ $errors->first('username') }}</div>
                        @endif
                    </div>
                    <div class="mb-3">
                        <label class="form-label required">Email</label>
                        <input type="email" name="email" value="{{ old('email') }}" class="form-control" placeholder="Nhập Email" autocomplete="new-password" value="{{ old('email') }}" />
                        @if ($errors->first('email'))
                        <div class="form-text text-danger">{{ $errors->first('email') }}</div>
                        @endif
                    </div>
                    <div class="mb-3">
                        <label class="form-label required">Mật khẩu</label>
                        <div class="input-group input-group-flat">
                            <input type="password" name="password" class="form-control" placeholder="Nhập mật khẩu" autocomplete="new-password" value="{{ old('password') }}" />
                        </div>
                        @if ($errors->first('password'))
                        <div class="form-text text-danger">{{ $errors->first('password') }}</div>
                        @endif
                    </div>
                    <div class="mb-3">
                        <label class="form-label required">Nhập lại mật khẩu</label>
                        <div class="input-group input-group-flat">
                            <input type="password" name="repassword" class="form-control" placeholder="Nhập lại mật khẩu" autocomplete="new-password" value="{{ old('repassword') }}" />
                        </div>
                        @if ($errors->first('repassword'))
                        <div class="form-text text-danger">{{ $errors->first('repassword') }}</div>
                        @endif
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Tên hiển thị</label>
                        <input type="text" name="name" value="{{ old('name') }}" class="form-control" placeholder="Nhập Tên hiển thị" autocomplete="new-password" value="{{ old('name') }}" />
                        @if ($errors->first('name'))
                        <div class="form-text text-danger">{{ $errors->first('name') }}</div>
                        @endif
                    </div>
                    <div class="mb-3 d-none">
                        <label class="form-check">
                            <input type="checkbox" class="form-check-input">
                            <span class="form-check-label">Đồng ý với <a href="#" tabindex="-1">điều khoản sử dụng</a>.</span>
                        </label>
                    </div>
                    <div class="form-footer">
                        <button type="submit" class="btn btn-primary w-100">
                            Đăng ký tài khoản
                        </button>
                    </div>
                </form>
            </div>
            {{-- <div class="hr-text">hoặc</div>
            <div class="card-body">
                <div class="row">
                    <div class="col">
                        <a href="#" class="btn w-100 bg-red text-white disabled" title="Đang phát triển" data-bs-toggle="tooltip" data-bs-placement="bottom">
                            <em class="ti ti-brand-google-filled me-2"></em>
                            Đăng nhập bằng Google
                        </a>
                    </div>
                </div>
            </div> --}}
        </div>
        <div class="text-center mt-3 text-white">
            Bạn đã có tài khoản?
            <a href="{{ route('login') }}" tabindex="-1" class="text-white"><strong>Đăng nhập</strong></a>
        </div>
    </div>
</div>
@endsection
