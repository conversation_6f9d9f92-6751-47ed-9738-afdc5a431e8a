<?php

namespace Dino\User\Http\Controllers;

use Dino\Base\Http\Controllers\BaseController;
use Dino\User\Models\User;
use Dino\User\Repositories\Interfaces\UserInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;


class AuthController extends BaseController
{
    protected $userRepository;

    public function __construct(UserInterface $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    public function login()
    {
        if (auth()->check()) {
            return redirect()->route('index');
        }

        return view('core/user::auth.login');
    }

    public function doLogin(Request $request)
    {
        $field = filter_var($request->input('login'), FILTER_VALIDATE_EMAIL) ? 'email' : 'username';
        $request->validate([
            'login' => ['required'],
            'password' => ['required'],
        ], [
            'login.required' => 'Tên đăng nhập/Email không được để trống',
            'password.required' => 'Mật khẩu không được để trống',
        ]);

        $request->merge([$field => $request->input('login')]);
        if (auth()->attempt($request->only($field, 'password'), $request->has('remember'))) {
            $request->session()->regenerate();
            return redirect()->route('index')->with('flash_data', [
                'type' => 'success',
                'message' => 'Đăng nhập thành công'
            ]);
        }

        return back()->withErrors([
            'login' => 'Thông tin đăng nhập không đúng',
        ])->onlyInput('login');
    }

    public function register()
    {
        if (auth()->check()) {
            return redirect()->route('public.index');
        }

        return view('core/user::auth.register');
    }

    public function doRegister(Request $request)
    {
        $request->validate([
            'username' => 'required|string|regex:/^[a-z0-9]+$/i|unique:users,username',
            'email' => 'required|email|unique:users,email',
            'password' => 'required',
            'repassword' => 'required|same:password',
        ], [
            'username.required' => 'Chưa nhập Tên đăng nhập',
            'username.regex' => 'Tên đăng nhập chỉ được dùng chữ (a-z) và số (0-9)',
            'username.unique' => 'Tên đăng nhập đã tồn tại, hãy chọn tên khác',
            'email.required' => 'Chưa nhập Email',
            'email.email' => 'Email không đúng định dạng',
            'email.unique' => 'Email đã tồn tại, hãy chọn email khác',
            'password.required' => 'Chưa nhập Mật khẩu',
            'repassword.required' => 'Chưa nhập lại mật khẩu',
            'repassword.same' => 'Hai mật khẩu không khớp',
        ]);

        $user = $this->userRepository->create([
            'name' => $request->input('name') ?? strtolower($request->input('username')),
            'username' => strtolower($request->input('username')),
            'email' => $request->input('email'),
            'password' => Hash::make($request->input('password')),
        ]);
        $user->assignRole('user');

        return redirect()->route('login')->with('flash_data', [
            'type' => 'success',
            'message' => 'Đăng ký tài khoản thành công. Bạn có thể đăng nhập ngay bây giờ!'
        ]);
    }

    public function forgot()
    {
        return view('core/user::auth.forgot');
    }

    public function sendResetLink(Request $request)
    {
        $request->validate([
            'email' => 'required|email|exists:users,email'
        ], [
            'email.required' => 'Bạn chưa nhập email',
            'email.email' => 'Địa chỉ email không hợp lệ',
            'email.exists' => 'Email này không tồn tại trong hệ thống'
        ]);

        $user = User::where('email', $request->email)->first();
        $remember_token = Str::random(60);

        // Lưu token
        $user->remember_token = $remember_token;
        $user->save();

        $resetLink = url('/auth/reset' . '?token=' .  $remember_token . '&email=' . urlencode($request->email));

        // Gửi email
        Mail::send('core/user::auth.send_mail', compact('resetLink'), function ($message) use ($request) {
            $message->to($request->email);
            $message->subject('Đặt lại mật khẩu');
        });

        return redirect()->back()->with('flash_data', [
            'type' => 'success',
            'message' => 'Gửi thành công. Vui lòng kiểm tra email!'
        ]);
    }

    public function showResetForm(Request $request)
    {
        return view('core/user::auth.reset_password', [
            'token' => $request->query('token'),
            'email' => $request->query('email')
        ]);
    }

    public function resetPassword(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|confirmed|min:8',
            'token' => 'required'
        ], [
            'password.required' => 'Bạn chưa nhập mật khẩu.',
            'email.required' => 'Bạn chưa nhập địa chỉ email.',
            'email.email' => 'Địa chỉ email không hợp lệ.',
            'password.confirmed' => 'Mật khẩu xác nhận không khớp.',
            'password.min' => 'Mật khẩu phải có ít nhất 8 ký tự.',
            'token.required' => 'Bạn chưa nhập mã xác thực.'
        ]);

        $user = User::where('email', $request->email)->first();

        // Kiểm tra token có hợp lệ không
        if (!$user || !hash_equals($user->remember_token, $request->token)) {
            return redirect()->back()->with('flash_data', [
                'type' => 'error',
                'message' => 'Email không hợp lệ hoặc token đã hết hạn.'
            ]);
        }

        $user->password = Hash::make($request->password);
        $user->remember_token = Str::random(60);
        $user->save();

        return redirect()->route('login')->with('flash_data', [
            'type' => 'success',
            'message' => 'Mật khẩu đã được cập nhật thành công.'
        ]);
    }

    public function logout()
    {
        auth()->logout();
        return redirect()->route('login');
    }
}
