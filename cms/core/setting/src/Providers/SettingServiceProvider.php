<?php

namespace Dino\Setting\Providers;

use <PERSON>\Base\Facades\DashboardMenu;
use <PERSON>\Base\Traits\LoadAndPublishDataTrait;
use <PERSON>\Setting\Facades\Setting;
use <PERSON>\Setting\Repositories\Eloquent\SettingRepository;
use <PERSON>\Setting\Repositories\Interfaces\SettingInterface;
use <PERSON>\Setting\Supports\DatabaseSettingStore;
use Dino\Setting\Supports\SettingStore;
use Illuminate\Foundation\AliasLoader;
use Illuminate\Routing\Events\RouteMatched;
use Illuminate\Support\ServiceProvider;

class SettingServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->setNamespace('core/setting')
            ->loadHelpers();

        $this->app->singleton(SettingStore::class, function () {
            return new DatabaseSettingStore();
        });

        $this->app->bind(SettingInterface::class, SettingRepository::class);

        if (!class_exists('Setting')) {
            AliasLoader::getInstance()->alias('Setting', Setting::class);
        }
    }

    public function boot(): void
    {
        $this->loadAndPublishViews()
            ->loadAndPublishTranslations()
            ->loadRoutes()
            ->loadMigrations()
            ->publishAssets();

        $this->app['events']->listen(RouteMatched::class, function () {
            DashboardMenu::registerItem([
                'id' => 'cms-core-system-setting',
                'priority' => 0,
                'parent_id' => 'cms-core-system',
                'name' => 'Cấu hình chung',
                'icon' => null,
                'url' => route('setting.admin.index'),
                'permissions' => [],
            ]);
        });

        // Ghi đè các setting mặc định của Laravel bằng thông tin setting trong admin
        $this->app->booted(function () {
            $config = $this->app->make('config');
            $config->set([
                'mail' => array_merge($config->get('mail'), [
                    'default' => 'smtp',
                    'from' => [
                        'address' => setting('mail_sender_email', $config->get('mail.from.address')),
                        'name' => setting('mail_sender_name', $config->get('mail.from.name')),
                    ],
                    'mailers' => [
                        'smtp' => [
                            'transport' => 'smtp',
                            'host' => setting('mail_host', $config->get('mail.mailers.smtp.host')),
                            'port' => setting('mail_port', $config->get('mail.mailers.smtp.port')),
                            'encryption' => setting('mail_encryption', $config->get('mail.mailers.smtp.encryption')),
                            'username' => setting('mail_username', $config->get('mail.mailers.smtp.username')),
                            'password' => setting('mail_password', $config->get('mail.mailers.smtp.password')),
                        ],
                    ],
                ]),
            ]);
        });
    }

    public function provides()
    {
        return [
            SettingStore::class
        ];
    }
}
