{"name": "cadami/dino-cms", "version": "0.0.01", "description": "The Dino CMS based on Laravel Framework.", "keywords": ["cadami", "dino cms", "dino platform"], "homepage": "https://dino.cadami.vn", "support": {"document": "https://dino.cadami.vn/docs"}, "authors": [{"name": "Code 9", "email": "<EMAIL>"}], "type": "project", "require": {}, "autoload": {"psr-4": {"Dino\\Base\\": "base/src", "Dino\\Setting\\": "setting/src", "Dino\\Media\\": "media/src", "Dino\\Dashboard\\": "dashboard/src", "Dino\\User\\": "user/src", "Dino\\Module\\": "module/src", "Dino\\Theme\\": "theme/src"}}, "extra": {"laravel": {"providers": ["Dino\\Base\\Providers\\BaseServiceProvider", "Dino\\Setting\\Providers\\SettingServiceProvider", "Dino\\Media\\Providers\\MediaServiceProvider", "Dino\\Dashboard\\Providers\\DashboardServiceProvider", "Dino\\User\\Providers\\UserServiceProvider", "Dino\\Module\\Providers\\ModuleServiceProvider", "Dino\\Theme\\Providers\\ThemeServiceProvider"], "aliases": {"BaseHelper": "Dino\\Base\\Facades\\BaseHelper", "Setting": "Dino\\Setting\\Facades\\Setting", "DashboardMenu": "Dino\\Base\\Facades\\DashboardMenu", "Theme": "Dino\\Theme\\Facades\\Theme", "ThemeManager": "Dino\\Theme\\Facades\\Manager"}}}, "minimum-stability": "dev", "prefer-stable": true}