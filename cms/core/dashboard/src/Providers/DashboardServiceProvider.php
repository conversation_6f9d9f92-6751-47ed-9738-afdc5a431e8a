<?php

namespace Dino\Dashboard\Providers;

use <PERSON>\Base\Facades\DashboardMenu;
use <PERSON>\Base\Traits\LoadAndPublishDataTrait;
use Illuminate\Routing\Events\RouteMatched;
use Illuminate\Support\ServiceProvider;

class DashboardServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->setNamespace('core/dashboard')
            ->loadHelpers()
            ->loadAndPublishViews()
            ->loadAndPublishTranslations()
            ->loadRoutes()
            ->loadMigrations()
            ->publishAssets();
    }

    public function boot(): void
    {
        $this->app['events']->listen(RouteMatched::class, function () {
            DashboardMenu::registerItem([
                'id' => 'cms-core-dashboard',
                'priority' => 0,
                'parent_id' => null,
                'name' => 'Bảng điều khiển',
                'icon' => 'ti ti-home',
                'url' => route('dashboard.index'),
                'permissions' => [],
            ]);
        });
    }
}
