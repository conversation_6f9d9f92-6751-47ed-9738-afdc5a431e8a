<?php

namespace Dino\Theme\Http\Controllers;

use Dino\Base\Http\Controllers\BaseController;
use <PERSON>\Theme\Facades\Theme;

class PublicController extends BaseController
{
    public function getIndex()
    {
        return Theme::scope('index')->render();
    }

    public function getPage($slug)
    {
        if (is_module_active('page')) {
            return \Dino\Page\Http\Controllers\PublicController::getPage($slug);
        }
        return abort(404);
    }
}
