<?php

use Dino\Theme\Http\Controllers\PublicController;
use Illuminate\Support\Facades\Route;

Route::group(['controller' => PublicController::class, 'middleware' => ['web']], function () {
    Route::group(apply_filters(BASE_FILTER_GROUP_PUBLIC_ROUTE, []), function () {
        Route::get('/', [
            'as' => 'index',
            'uses' => 'getIndex',
        ]);

        Route::get('{slug}', [
            'as' => 'page',
            'uses' => 'getPage',
        ]);
    });
});
