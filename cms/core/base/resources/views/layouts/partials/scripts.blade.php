<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1000">
    <div id="toast-admin" class="toast align-items-center text-white bg-info border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body"></div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>
<script src="{{ asset('assets/js/tabler.min.js') }}"></script>
<script src="{{ asset('cms/js/admin.js') }}"></script>

<script type="text/javascript">
    var inProcess = false;
    $(document).ready(() => {
        @if (isset($errors) && count($errors->all()) > 0)
            @foreach ($errors->all() as $message)
            showNotify('{{ $message }}', 'error');
            @endforeach
        @endif

        @if (session('flash_data'))
            @php
                $flash_data = session('flash_data');
            @endphp
            showNotify("{{ $flash_data['message'] }}", "{{ $flash_data['type'] }}");
        @endif
    });

</script>
