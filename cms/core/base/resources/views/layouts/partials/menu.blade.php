<header class="navbar-expand-md">
    <div class="collapse navbar-collapse" id="navbar-menu">
        <div class="navbar">
            <div class="container-xl">
                <div class="row flex-fill align-items-center">
                    <div class="col">
                        <ul class="navbar-nav">
                            @foreach ($menus = DashboardMenu::getAll() as $menu)
                            @php $menu = apply_filters(BASE_FILTER_DASHBOARD_MENU, $menu); @endphp
                            <li class="nav-item @if ($menu['active']) active @endif @if (isset($menu['children']) && count($menu['children'])) dropdown @endif">
                                <a class="nav-link @if (isset($menu['children']) && count($menu['children'])) dropdown-toggle @endif" href="{{ $menu['url'] }}"
                                @if (isset($menu['children']) && count($menu['children'])) data-bs-toggle="dropdown" data-bs-auto-close="outside" @endif
                                 role="button" aria-expanded="true">
                                    @if ($menu['icon'])
                                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                                        <i class="icon {{ $menu['icon'] }}"></i>
                                    </span>
                                    @endif
                                    <span class="nav-link-title">
                                        {{ !is_array(trans($menu['name'])) ? trans($menu['name']) : null }}
                                        {!! apply_filters(BASE_FILTER_APPEND_MENU_NAME, null, $menu['id']) !!}
                                    </span>
                                </a>
                                @if (isset($menu['children']) && count($menu['children']))
                                <div class="dropdown-menu">
                                    @foreach ($menu['children'] as $item)
                                    <a class="dropdown-item @if ($item['active']) active @endif" href="{{ $item['url'] }}" id="{{ $item['id'] }}">
                                        @if ($item['icon'])
                                        <i class="{{ $item['icon'] }}"></i>
                                        @endif
                                        {{ trans($item['name']) }}
                                        {!! apply_filters(BASE_FILTER_APPEND_MENU_NAME, null, $item['id']) !!}
                                    </a>
                                    @endforeach
                                </div>
                                @endif
                            </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>
