<?php

use GuzzleHttp\Client;

if (!function_exists('notify_telegram')) {
    function notify_telegram($content = '')
    {
        if (!empty($content)) {
            try {
                $client = new Client();
                $url_request = 'https://api.telegram.org/bot7448175449:AAEvSaE7c9NdDm4qU3nhE-Ohr4BK-akiLPY/sendMessage?';
                $url_request .= 'chat_id=' . config('telegram.id_chat_tele_common');
                $url_request .= '&parse_mode=html';
                $url_request .= '&text=' . trim(urlencode($content), "_\n");
                return $client->request('GET', $url_request);
            } catch (\Throwable $th) {
                return $th->getMessage();
            }
        }
        return false;
    }
}

if (!function_exists('notify_telegram_digital_seo')) {
    function notify_telegram_digital_seo($content = '')
    {
        if (!empty($content)) {
            try {
                $client = new Client();
                $url_request = 'https://api.telegram.org/bot7448175449:AAEvSaE7c9NdDm4qU3nhE-Ohr4BK-akiLPY/sendMessage?';
                $url_request .= 'chat_id=' . config('telegram.id_chat_tele_digital_seo');
                $url_request .= '&parse_mode=html';
                $url_request .= '&text=' . trim(urlencode($content), "_\n");
                return $client->request('GET', $url_request);
            } catch (\Throwable $th) {
                return $th->getMessage();
            }
        }
        return false;
    }
}

if (!function_exists('notify_telegram_dev')) {
    function notify_telegram_dev($content = '')
    {
        if (!empty($content)) {
            try {
                $client = new Client();
                $url_request = 'https://api.telegram.org/bot7448175449:AAEvSaE7c9NdDm4qU3nhE-Ohr4BK-akiLPY/sendMessage?';
                $url_request .= 'chat_id=-4599215517';
                $url_request .= '&parse_mode=html';
                $url_request .= '&text=' . trim(urlencode($content), "_\n");
                return $client->request('GET', $url_request);
            } catch (\Throwable $th) {
                return $th->getMessage();
            }
        }
        return false;
    }
}
