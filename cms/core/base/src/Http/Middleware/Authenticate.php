<?php

namespace Dino\Base\Http\Middleware;

use Closure;
use Dino\Base\Facades\BaseHelper;
use Illuminate\Auth\Middleware\Authenticate as Middleware;

class Authenticate extends Middleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next, ...$guards)
    {
        $this->authenticate($request, $guards);
        if (!$guards) {
            if (
                $request->is(BaseHelper::getAdminPrefix() . '/*') ||
                $request->is(BaseHelper::getAdminPrefix())
            ) {
                if (!$request->user()->hasRole(SUPERADMIN_ROLE_NAME)) {
                    return redirect()->route('userdashboard');
                }
            }
        }
        return $next($request);
    }

    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string|null
     */
    protected function redirectTo($request)
    {
        if (!$request->expectsJson()) {
            return route('login');
        }
    }
}
