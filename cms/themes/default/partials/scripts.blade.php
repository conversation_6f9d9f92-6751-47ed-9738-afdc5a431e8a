<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 9999">
    <div id="toast-site" class="toast align-items-center text-white bg-info border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body duy1"></div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>

<script src="{{ asset('cms/js/web.js') }}"></script>
<script src="{{ asset('ckeditor/ckeditor.js') }}"></script>
<script type="text/javascript">
    function copyToClipboard(el) {
        var copyText = $(el).parent().find('input').first();
        copyText.select();
        document.execCommand("copy");
        alert('Đã sao chép');
    }

    $(document).ready(() => {
        @if (session('flash_data'))
            @php
                $flash_data = session('flash_data');
            @endphp
            showNotify("{{ $flash_data['message'] }}", "{{ $flash_data['type'] }}");
        @endif
    });
</script>
