<?php

namespace Dino\Page\Providers;

use Dino\Base\Traits\LoadAndPublishDataTrait;
use Illuminate\Support\ServiceProvider;

class PageServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        //
    }

    public function boot(): void
    {
        $this->setNamespace('modules/page')
            ->loadHelpers()
            ->loadAndPublishViews()
            ->loadAndPublishTranslations()
            ->loadRoutes()
            ->loadMigrations()
            ->publishAssets();

    }
}
