name: 'dinocms'
services:

  # PHP
  app:
    build:
      context: ./php/
      dockerfile: Dockerfile
    image: dino/php8.4-fpm
    container_name: dinocms_app
    restart: unless-stopped
    tty: true
    environment:
      SERVICE_NAME: app
      SERVICE_TAGS: dev
    working_dir: /var/www
    volumes:
      - ./../../:/var/www
      - ./php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - dinocms-network

  # NGINX
  webserver:
    image: nginx:alpine
    container_name: dinocms_webserver
    restart: unless-stopped
    tty: true
    ports:
      - "8000:80"
    volumes:
      - ./../../:/var/www
      - ./nginx/:/etc/nginx/conf.d/
    networks:
      - dinocms-network
    depends_on:
      - app

  # MariaDB
  db:
    image: mariadb:latest
    container_name: dinocms_db
    restart: unless-stopped
    tty: true
    environment:
      MYSQL_DATABASE: dinocms
      MYSQL_ROOT_PASSWORD: password
      SERVICE_TAGS: dev
      SERVICE_NAME: mariadb
    volumes:
      - dbdata:/var/lib/mysql/
      - ./mariadb/my.cnf:/etc/mysql/my.cnf
    networks:
      - dinocms-network

  # phpMyAdmin
  phpmyadmin:
    container_name: dinocms_phpmyadmin
    image: phpmyadmin:latest
    environment:
      PMA_HOST: dinocms_db
      PMA_USER: root
      PMA_PASSWORD: password
      MYSQL_ROOT_PASSWORD: password
    ports:
      - "8001:80"
    depends_on:
      - db
    networks:
      - dinocms-network

# Networks
networks:
  dinocms-network:
    driver: bridge

# Volumes
volumes:
  dbdata:
    driver: local
